#include "SC7I22_Library.h"

// 寄存器地址定义
#define SC7I22_WHO_AM_I         0x01
#define SC7I22_STATUS_REG       0x0B
#define SC7I22_ACC_DATA_START   0x0C
#define SC7I22_TIMESTAMP_0      0x18
#define SC7I22_TIMESTAMP_1      0x19
#define SC7I22_TIMESTAMP_2      0x20
#define SC7I22_ACC_CONF         0x40
#define SC7I22_ACC_RANGE        0x41
#define SC7I22_GYR_CONF         0x42
#define SC7I22_GYR_RANGE        0x43
#define SC7I22_I2C_CFG          0x05
#define SC7I22_SOFT_RESET       0x7D
#define SC7I22_BANK_SELECT      0x7F

SC7I22::SC7I22(uint8_t sdo_pin_state, uint8_t sda_pin, uint8_t scl_pin) {
  _sda_pin = sda_pin;
  _scl_pin = scl_pin;
  
  // 根据SDO引脚状态确定I2C地址
  if (sdo_pin_state == SC7I22_SDO_TO_GND) {
    _i2c_addr = 0x18;
  } else {
    _i2c_addr = 0x19;
  }
  
  // 默认配置
  _acc_range = SC7I22_ACC_2G;
  _gyr_range = SC7I22_GYR_2000DPS;
  updateScales();
}

bool SC7I22::begin(uint32_t i2c_freq) {
  // 初始化I2C
  Wire.begin(_sda_pin, _scl_pin);
  Wire.setClock(i2c_freq);
  
  // 检查连接
  if (!checkConnection()) {
    return false;
  }
  
  // 软复位
  writeReg(SC7I22_SOFT_RESET, 0x0E);
  delay(100);
  
  // 默认配置：100Hz采样率，±2G，±2000dps
  setSampleRate(SC7I22_100HZ, SC7I22_100HZ);
  setAccRange(SC7I22_ACC_2G);
  setGyrRange(SC7I22_GYR_2000DPS);
  
  // I2C接口配置
  writeReg(SC7I22_I2C_CFG, 0x50);
  
  delay(100);
  return true;
}

bool SC7I22::writeReg(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(_i2c_addr);
  Wire.write(reg);
  Wire.write(data);
  return (Wire.endTransmission() == 0);
}

bool SC7I22::readReg(uint8_t reg, uint8_t* data, uint8_t len) {
  Wire.beginTransmission(_i2c_addr);
  Wire.write(reg);
  if (Wire.endTransmission() != 0) return false;
  
  Wire.requestFrom(_i2c_addr, len);
  for (int i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      return false;
    }
  }
  return true;
}

bool SC7I22::checkConnection() {
  uint8_t who_am_i = 0;
  
  // 切换到bank 0
  writeReg(SC7I22_BANK_SELECT, 0x00);
  
  if (readReg(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    return (who_am_i == 0x6A);
  }
  return false;
}

bool SC7I22::setAccRange(SC7I22_AccRange range) {
  _acc_range = range;
  updateScales();
  return writeReg(SC7I22_ACC_RANGE, (uint8_t)range);
}

bool SC7I22::setGyrRange(SC7I22_GyrRange range) {
  _gyr_range = range;
  updateScales();
  return writeReg(SC7I22_GYR_RANGE, (uint8_t)range);
}

bool SC7I22::setSampleRate(SC7I22_SampleRate acc_rate, SC7I22_SampleRate gyr_rate) {
  bool result = true;
  result &= writeReg(SC7I22_ACC_CONF, (uint8_t)acc_rate);
  result &= writeReg(SC7I22_GYR_CONF, (uint8_t)gyr_rate);
  return result;
}

void SC7I22::updateScales() {
  // 计算加速度计比例因子
  switch (_acc_range) {
    case SC7I22_ACC_2G:  _acc_scale = 2.0 / 32768.0; break;
    case SC7I22_ACC_4G:  _acc_scale = 4.0 / 32768.0; break;
    case SC7I22_ACC_8G:  _acc_scale = 8.0 / 32768.0; break;
    case SC7I22_ACC_16G: _acc_scale = 16.0 / 32768.0; break;
  }
  
  // 计算陀螺仪比例因子
  switch (_gyr_range) {
    case SC7I22_GYR_125DPS:  _gyr_scale = 125.0 / 32768.0; break;
    case SC7I22_GYR_250DPS:  _gyr_scale = 250.0 / 32768.0; break;
    case SC7I22_GYR_500DPS:  _gyr_scale = 500.0 / 32768.0; break;
    case SC7I22_GYR_1000DPS: _gyr_scale = 1000.0 / 32768.0; break;
    case SC7I22_GYR_2000DPS: _gyr_scale = 2000.0 / 32768.0; break;
  }
}

bool SC7I22::readData(SC7I22_Data& data) {
  uint8_t raw_data[12];
  uint8_t status = 0;
  uint16_t timeout = 0;
  
  // 等待数据就绪
  while ((status & 0x03) != 0x03) {
    if (!readReg(SC7I22_STATUS_REG, &status, 1)) {
      return false;
    }
    timeout++;
    if (timeout > 1000) return false;
    delay(1);
  }
  
  // 读取12字节原始数据
  if (!readReg(SC7I22_ACC_DATA_START, raw_data, 12)) {
    return false;
  }
  
  // 解析原始数据
  data.acc_x = (int16_t)((raw_data[0] << 8) | raw_data[1]);
  data.acc_y = (int16_t)((raw_data[2] << 8) | raw_data[3]);
  data.acc_z = (int16_t)((raw_data[4] << 8) | raw_data[5]);
  
  data.gyr_x = (int16_t)((raw_data[6] << 8) | raw_data[7]);
  data.gyr_y = (int16_t)((raw_data[8] << 8) | raw_data[9]);
  data.gyr_z = (int16_t)((raw_data[10] << 8) | raw_data[11]);
  
  // 转换为物理单位
  data.acc_g_x = data.acc_x * _acc_scale;
  data.acc_g_y = data.acc_y * _acc_scale;
  data.acc_g_z = data.acc_z * _acc_scale;
  
  data.gyr_dps_x = data.gyr_x * _gyr_scale;
  data.gyr_dps_y = data.gyr_y * _gyr_scale;
  data.gyr_dps_z = data.gyr_z * _gyr_scale;
  
  // 读取时间戳
  data.timestamp = readTimestamp();
  
  return true;
}

bool SC7I22::readRawData(int16_t acc[3], int16_t gyr[3]) {
  SC7I22_Data data;
  if (readData(data)) {
    acc[0] = data.acc_x;
    acc[1] = data.acc_y;
    acc[2] = data.acc_z;
    gyr[0] = data.gyr_x;
    gyr[1] = data.gyr_y;
    gyr[2] = data.gyr_z;
    return true;
  }
  return false;
}

uint32_t SC7I22::readTimestamp() {
  uint8_t time_data[3];
  
  readReg(SC7I22_TIMESTAMP_0, &time_data[0], 1);
  readReg(SC7I22_TIMESTAMP_1, &time_data[1], 1);
  readReg(SC7I22_TIMESTAMP_2, &time_data[2], 1);
  
  return (uint32_t)(time_data[0] << 16 | time_data[1] << 8 | time_data[2]);
}

bool SC7I22::powerDown() {
  return writeReg(SC7I22_SOFT_RESET, 0x00);
}

#endif

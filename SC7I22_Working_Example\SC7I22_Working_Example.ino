#include <Wire.h>

// SC7I22 IMU配置参数
#define SC7I22_SDO_VDD_GND            1    // SDO引脚接VDD
#define SC7I22_IIC_7BITS_8BITS        0    // 使用7位I2C地址

// I2C地址配置
#if SC7I22_SDO_VDD_GND == 0
  #define SC7I22_IIC_ADDR        0x18
#else
  #define SC7I22_IIC_ADDR        0x19
#endif

// 寄存器地址定义
#define SC7I22_WHO_AM_I         0x01
#define SC7I22_STATUS_REG       0x0B
#define SC7I22_ACC_DATA_START   0x0C
#define SC7I22_SOFT_RESET       0x7D
#define SC7I22_ACC_CONF         0x40
#define SC7I22_ACC_RANGE        0x41
#define SC7I22_GYR_CONF         0x42
#define SC7I22_GYR_RANGE        0x43
#define SC7I22_I2C_CFG          0x05
#define SC7I22_BANK_SELECT      0x7F

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 数据结构
struct IMU_Data {
  int16_t acc_x, acc_y, acc_z;    // 加速度计数据
  int16_t gyr_x, gyr_y, gyr_z;    // 陀螺仪数据
  float acc_g_x, acc_g_y, acc_g_z; // 加速度计数据(g)
  float gyr_dps_x, gyr_dps_y, gyr_dps_z; // 陀螺仪数据(度/秒)
};

IMU_Data imu_data;

// I2C写入函数
bool SC7I22_WriteReg(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(SC7I22_IIC_ADDR);
  Wire.write(reg);
  Wire.write(data);
  uint8_t result = Wire.endTransmission();
  if (result != 0) {
    Serial.printf("写入寄存器0x%02X失败，错误代码: %d\n", reg, result);
    return false;
  }
  return true;
}

// I2C读取函数
bool SC7I22_ReadReg(uint8_t reg, uint8_t* data, uint8_t len) {
  Wire.beginTransmission(SC7I22_IIC_ADDR);
  Wire.write(reg);
  uint8_t result = Wire.endTransmission();
  if (result != 0) {
    Serial.printf("请求寄存器0x%02X失败，错误代码: %d\n", reg, result);
    return false;
  }
  
  Wire.requestFrom(SC7I22_IIC_ADDR, len);
  for (int i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      Serial.printf("读取寄存器0x%02X数据不足\n", reg);
      return false;
    }
  }
  return true;
}

// SC7I22连接检查
bool SC7I22_Check() {
  uint8_t who_am_i = 0;
  
  Serial.printf("检查SC7I22连接 (地址: 0x%02X)...\n", SC7I22_IIC_ADDR);
  
  // 切换到bank 0
  if (!SC7I22_WriteReg(SC7I22_BANK_SELECT, 0x00)) {
    Serial.println("❌ 无法切换到Bank 0");
    return false;
  }
  
  delay(10);
  
  if (SC7I22_ReadReg(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.printf("WHO_AM_I寄存器: 0x%02X ", who_am_i);
    if (who_am_i == 0x6A) {
      Serial.println("✓ SC7I22识别成功！");
      return true;
    } else {
      Serial.println("❌ WHO_AM_I值不正确（期望0x6A）");
      return false;
    }
  } else {
    Serial.println("❌ 无法读取WHO_AM_I寄存器");
    return false;
  }
}

// SC7I22初始化配置
bool SC7I22_Config() {
  if (!SC7I22_Check()) {
    return false;
  }
  
  Serial.println("开始配置SC7I22...");
  
  // 软复位，进入正常工作模式
  Serial.println("执行软复位...");
  if (!SC7I22_WriteReg(SC7I22_SOFT_RESET, 0x0E)) return false;
  delay(100);
  
  // 配置加速度计：100Hz采样率
  Serial.println("配置加速度计...");
  if (!SC7I22_WriteReg(SC7I22_ACC_CONF, 0x88)) return false;
  
  // 配置加速度计量程：±2G
  if (!SC7I22_WriteReg(SC7I22_ACC_RANGE, 0x01)) return false;
  
  // 配置陀螺仪：100Hz采样率
  Serial.println("配置陀螺仪...");
  if (!SC7I22_WriteReg(SC7I22_GYR_CONF, 0xC8)) return false;
  
  // 配置陀螺仪量程：±2000dps
  if (!SC7I22_WriteReg(SC7I22_GYR_RANGE, 0x00)) return false;
  
  // I2C接口配置
  Serial.println("配置I2C接口...");
  if (!SC7I22_WriteReg(SC7I22_I2C_CFG, 0x50)) return false;
  
  // 等待传感器稳定
  delay(200);
  
  // 验证配置
  uint8_t acc_conf, acc_range, gyr_conf, gyr_range;
  SC7I22_ReadReg(SC7I22_ACC_CONF, &acc_conf, 1);
  SC7I22_ReadReg(SC7I22_ACC_RANGE, &acc_range, 1);
  SC7I22_ReadReg(SC7I22_GYR_CONF, &gyr_conf, 1);
  SC7I22_ReadReg(SC7I22_GYR_RANGE, &gyr_range, 1);
  
  Serial.printf("配置验证 - ACC_CONF:0x%02X, ACC_RANGE:0x%02X, GYR_CONF:0x%02X, GYR_RANGE:0x%02X\n",
                acc_conf, acc_range, gyr_conf, gyr_range);
  
  Serial.println("✓ SC7I22配置完成！");
  return true;
}

// 实时读取原始数据函数
bool SC7I22_RawData_Read(signed short * acc_data_buf, signed short * gyr_data_buf) {
  unsigned char raw_data[12];
  unsigned char drdy_status = 0;
  unsigned short drdy_cnt = 0;

  // 等待数据就绪
  while ((drdy_status & 0x03) != 0x03) {
    drdy_status = 0x00;
    if (!SC7I22_ReadReg(SC7I22_STATUS_REG, &drdy_status, 1)) {
      Serial.println("读取状态寄存器失败");
      return false;
    }
    drdy_cnt++;
    if (drdy_cnt > 1000) {
      Serial.printf("数据就绪超时，状态: 0x%02X\n", drdy_status);
      return false;
    }
    delay(1);
  }

  // 读取12字节原始数据
  if (!SC7I22_ReadReg(SC7I22_ACC_DATA_START, raw_data, 12)) {
    Serial.println("读取原始数据失败");
    return false;
  }

  // 解析加速度计数据（高字节在前）
  acc_data_buf[0] = (signed short)((raw_data[0] << 8) | raw_data[1]);  // ACCX
  acc_data_buf[1] = (signed short)((raw_data[2] << 8) | raw_data[3]);  // ACCY
  acc_data_buf[2] = (signed short)((raw_data[4] << 8) | raw_data[5]);  // ACCZ
  
  // 解析陀螺仪数据（高字节在前）
  gyr_data_buf[0] = (signed short)((raw_data[6] << 8) | raw_data[7]);   // GYRX
  gyr_data_buf[1] = (signed short)((raw_data[8] << 8) | raw_data[9]);   // GYRY
  gyr_data_buf[2] = (signed short)((raw_data[10] << 8) | raw_data[11]); // GYRZ

  return true;
}

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("ESP32-C3 SC7I22 IMU 工作示例");
  Serial.println("=============================");
  
  // 初始化I2C，使用较低时钟频率确保稳定性
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);  // 100kHz，更稳定
  delay(100);
  
  // 初始化SC7I22
  if (SC7I22_Config()) {
    Serial.println("🎉 SC7I22初始化成功，开始读取数据...");
    Serial.println();
  } else {
    Serial.println("❌ SC7I22初始化失败！");
    while(1) {
      delay(1000);
    }
  }
}

void loop() {
  signed short acc_data[3], gyr_data[3];
  
  // 读取IMU数据
  if (SC7I22_RawData_Read(acc_data, gyr_data)) {
    // 转换为物理单位
    // 加速度计：±2G量程，16位分辨率
    float acc_scale = 2.0 / 32768.0;  // g/LSB
    float acc_g_x = acc_data[0] * acc_scale;
    float acc_g_y = acc_data[1] * acc_scale;
    float acc_g_z = acc_data[2] * acc_scale;
    
    // 陀螺仪：±2000dps量程，16位分辨率
    float gyr_scale = 2000.0 / 32768.0;  // dps/LSB
    float gyr_dps_x = gyr_data[0] * gyr_scale;
    float gyr_dps_y = gyr_data[1] * gyr_scale;
    float gyr_dps_z = gyr_data[2] * gyr_scale;
    
    // 打印数据
    Serial.printf("ACC(g): X=%6.3f, Y=%6.3f, Z=%6.3f | ", acc_g_x, acc_g_y, acc_g_z);
    Serial.printf("GYR(dps): X=%7.2f, Y=%7.2f, Z=%7.2f\n", gyr_dps_x, gyr_dps_y, gyr_dps_z);
    
  } else {
    Serial.println("❌ 读取IMU数据失败！");
  }
  
  delay(100);  // 100ms读取一次，对应100Hz采样率
}

#include "SC7I22_Library.h"

// 创建SC7I22实例
// 参数：SDO引脚状态(1=接VDD, 0=接GND), SDA引脚, SCL引脚
SC7I22 imu(SC7I22_SDO_TO_VDD, 8, 9);

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("ESP32-C3 SC7I22 IMU 简单示例");
  Serial.println("==============================");
  
  // 初始化IMU
  if (imu.begin()) {
    Serial.println("✓ SC7I22初始化成功！");
    
    // 可选：配置不同的量程和采样率
    // imu.setAccRange(SC7I22_ACC_4G);      // 设置加速度计量程为±4G
    // imu.setGyrRange(SC7I22_GYR_1000DPS); // 设置陀螺仪量程为±1000dps
    // imu.setSampleRate(SC7I22_200HZ, SC7I22_200HZ); // 设置采样率为200Hz
    
  } else {
    Serial.println("✗ SC7I22初始化失败！");
    Serial.println("请检查：");
    Serial.println("1. I2C接线是否正确");
    Serial.println("2. SDO引脚接线是否与代码配置一致");
    Serial.println("3. 电源供电是否正常");
    while(1) delay(1000);
  }
}

void loop() {
  SC7I22_Data data;
  
  // 读取IMU数据
  if (imu.readData(data)) {
    // 打印加速度计数据
    Serial.printf("ACC(g): X=%6.3f, Y=%6.3f, Z=%6.3f | ", 
                  data.acc_g_x, data.acc_g_y, data.acc_g_z);
    
    // 打印陀螺仪数据
    Serial.printf("GYR(dps): X=%7.2f, Y=%7.2f, Z=%7.2f\n", 
                  data.gyr_dps_x, data.gyr_dps_y, data.gyr_dps_z);
    
    // 可选：打印原始数据和时间戳
    // Serial.printf("Raw - ACC: %d,%d,%d  GYR: %d,%d,%d  Time: %lu\n",
    //               data.acc_x, data.acc_y, data.acc_z,
    //               data.gyr_x, data.gyr_y, data.gyr_z, data.timestamp);
    
  } else {
    Serial.println("读取IMU数据失败！");
  }
  
  delay(100);  // 100ms读取一次
}

/*
 * SC7I22 IMU Arduino测试代码
 * 基于原始驱动函数接口实现
 * 适用于ESP32-C3开发板
 */

#include <Wire.h>

// ===== 原始驱动配置参数 =====
#define SL_Sensor_Algo_Release_Enable  0x01
#define SL_SC7I22_FIFO_MODE_ENABLE    0x00  // 使用STREAM模式
#define SL_SC7I22_SDO_VDD_GND         1     // SDO接VDD
#define SL_SC7I22_IIC_7BITS_8BITS     0     // 使用7位I2C地址

// I2C地址配置
#if SL_SC7I22_SDO_VDD_GND==0
  #define SL_SC7I22_IIC_ADDRESS        0x18
#else
  #define SL_SC7I22_IIC_ADDRESS        0x19
#endif

// 寄存器定义
#define SC7I22_WHO_AM_I         0x01

// 接口选择
#define SL_SPI_IIC_INTERFACE    0x01  // 使用I2C接口

// ESP32-C3引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// ===== 实现原始驱动要求的I2C接口函数 =====

/**
 * I2C/SPI写入函数
 * @param sl_spi_iic: 0=SPI, 1=I2C
 * @param reg: 寄存器地址
 * @param dat: 写入数据
 * @return: 1=成功, 0=失败
 */
unsigned char SL_SC7I22_I2c_Spi_Write(unsigned char sl_spi_iic, unsigned char reg, unsigned char dat) {
  if (sl_spi_iic == 1) {  // I2C模式
    Wire.beginTransmission(SL_SC7I22_IIC_ADDRESS);
    Wire.write(reg);
    Wire.write(dat);
    return (Wire.endTransmission() == 0) ? 1 : 0;
  }
  return 0;  // 不支持SPI
}

/**
 * I2C/SPI读取函数
 * @param sl_spi_iic: 0=SPI, 1=I2C
 * @param reg: 寄存器地址
 * @param len: 读取长度
 * @param buf: 数据缓冲区
 * @return: 1=成功, 0=失败
 */
unsigned char SL_SC7I22_I2c_Spi_Read(unsigned char sl_spi_iic, unsigned char reg, unsigned short len, unsigned char* buf) {
  if (sl_spi_iic == 1) {  // I2C模式
    Wire.beginTransmission(SL_SC7I22_IIC_ADDRESS);
    Wire.write(reg);
    if (Wire.endTransmission() != 0) return 0;
    
    Wire.requestFrom(SL_SC7I22_IIC_ADDRESS, (uint8_t)len);
    for (int i = 0; i < len; i++) {
      if (Wire.available()) {
        buf[i] = Wire.read();
      } else {
        return 0;
      }
    }
    return 1;
  }
  return 0;  // 不支持SPI
}

// ===== 原始驱动函数声明 =====
unsigned char SL_SC7I22_Check(void);
unsigned char SL_SC7I22_Config(void);
unsigned int SL_SC7I22_TimeStamp_Read(void);
void SL_SC7I22_RawData_Read(signed short* acc_data_buf, signed short* gyr_data_buf);
unsigned char SL_SC7I22_POWER_DOWN(void);

// ===== 实现原始驱动函数 =====

// 软件延时函数
static void sl_delay(unsigned char sl_i) {
  delay(sl_i * 10);  // Arduino的delay函数，单位ms
}

// SC7I22传感器连接检查函数
unsigned char SL_SC7I22_Check(void) {
  unsigned char reg_value = 0;
  
  SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7F, 0x00);  // 切换到bank 0
  SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, SC7I22_WHO_AM_I, 1, &reg_value);
  
  Serial.printf("WHO_AM_I: 0x%02X\n", reg_value);
  
  if (reg_value == 0x6A)
    return 0x01;  // SC7I22检测成功
  else
    return 0x00;  // IIC通讯异常
}

// SC7I22传感器配置函数
unsigned char SL_SC7I22_Config(void) {
  unsigned char Check_Flag = 0;
  unsigned short drdy_cnt = 0;
  
  Check_Flag = SL_SC7I22_Check();
  
  if (Check_Flag == 1) {
    Serial.println("开始配置SC7I22...");
    
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7D, 0x0E);  // 软复位，进入正常工作模式
    sl_delay(10);
    
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x40, 0x88);  // ACC_CONF 加速度计配置：100Hz采样率
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x41, 0x01);  // ACC_RANGE 加速度计量程：±2G
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x42, 0xC8);  // GYR_CONF 陀螺仪配置：100Hz采样率
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x43, 0x00);  // GYR_RANGE 陀螺仪量程：±2000dps
    SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x05, 0x50);  // I2C_CFG I2C接口配置
    
    Serial.println("SC7I22配置完成");
    return 1;
  } else {
    return 0;
  }
}

// 读取传感器时间戳
unsigned int SL_SC7I22_TimeStamp_Read(void) {
  unsigned char time_data[3];
  unsigned int time_stamp;
  
  SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x18, 1, &time_data[0]);
  SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x19, 1, &time_data[1]);
  SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x20, 1, &time_data[2]);
  
  time_stamp = (unsigned int)(time_data[0] << 16 | time_data[1] << 8 | time_data[2]);
  
  return time_stamp;
}

// 实时读取原始数据函数
void SL_SC7I22_RawData_Read(signed short* acc_data_buf, signed short* gyr_data_buf) {
  unsigned char raw_data[12];
  unsigned char drdy_status = 0;
  unsigned short drdy_cnt = 0;
  
  // 等待数据就绪
  while ((drdy_status & 0x03) != 0x03) {
    drdy_status = 0x00;
    SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x0B, 1, &drdy_status);  // 读取状态寄存器
    drdy_cnt++;
    if (drdy_cnt > 1000) break;  // 超时保护
    sl_delay(1);
  }
  
  // 读取12字节原始数据
  SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x0C, 12, &raw_data[0]);
  
  // 解析加速度计数据（高字节在前）
  acc_data_buf[0] = (signed short)((((unsigned char)raw_data[0]) * 256) + ((unsigned char)raw_data[1]));   // ACCX-16位
  acc_data_buf[1] = (signed short)((((unsigned char)raw_data[2]) * 256) + ((unsigned char)raw_data[3]));   // ACCY-16位
  acc_data_buf[2] = (signed short)((((unsigned char)raw_data[4]) * 256) + ((unsigned char)raw_data[5]));   // ACCZ-16位
  
  // 解析陀螺仪数据（高字节在前）
  gyr_data_buf[0] = (signed short)((((unsigned char)raw_data[6]) * 256) + ((unsigned char)raw_data[7]));   // GYRX-16位
  gyr_data_buf[1] = (signed short)((((unsigned char)raw_data[8]) * 256) + ((unsigned char)raw_data[9]));   // GYRY-16位
  gyr_data_buf[2] = (signed short)((((unsigned char)raw_data[10]) * 256) + ((unsigned char)raw_data[11])); // GYRZ-16位
}

// 传感器进入低功耗模式
unsigned char SL_SC7I22_POWER_DOWN(void) {
  SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7D, 0x00);  // 进入POWER DOWN模式
  return 1;
}

// ===== Arduino主程序 =====

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== SC7I22 IMU Arduino测试程序 ===");
  Serial.println("基于原始驱动函数接口实现");
  Serial.println();
  
  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);  // 100kHz I2C时钟
  delay(100);
  
  Serial.printf("I2C初始化完成 - SDA: GPIO%d, SCL: GPIO%d\n", SDA_PIN, SCL_PIN);
  Serial.printf("SC7I22 I2C地址: 0x%02X\n", SL_SC7I22_IIC_ADDRESS);
  Serial.println();
  
  // 初始化SC7I22
  if (SL_SC7I22_Config()) {
    Serial.println("✓ SC7I22初始化成功！");
    Serial.println("开始读取IMU原始数据...");
    Serial.println();
  } else {
    Serial.println("✗ SC7I22初始化失败！");
    Serial.println("请检查硬件连接和配置");
    while (1) {
      delay(1000);
    }
  }
}

void loop() {
  signed short acc_data[3], gyr_data[3];
  unsigned int timestamp;
  
  // 调用原始驱动函数读取数据
  SL_SC7I22_RawData_Read(acc_data, gyr_data);
  
  // 读取时间戳
  timestamp = SL_SC7I22_TimeStamp_Read();
  
  // 转换为物理单位
  float acc_scale = 2.0 / 32768.0;      // ±2G量程，16位分辨率
  float gyr_scale = 2000.0 / 32768.0;   // ±2000dps量程，16位分辨率
  
  float acc_g[3], gyr_dps[3];
  for (int i = 0; i < 3; i++) {
    acc_g[i] = acc_data[i] * acc_scale;
    gyr_dps[i] = gyr_data[i] * gyr_scale;
  }
  
  // 打印原始数据和物理单位数据
  Serial.println("=== SC7I22 IMU数据 ===");
  Serial.printf("时间戳: %lu\n", timestamp);
  Serial.printf("加速度计原始值: X=%6d, Y=%6d, Z=%6d\n", acc_data[0], acc_data[1], acc_data[2]);
  Serial.printf("陀螺仪原始值:   X=%6d, Y=%6d, Z=%6d\n", gyr_data[0], gyr_data[1], gyr_data[2]);
  Serial.printf("加速度计(g):    X=%7.3f, Y=%7.3f, Z=%7.3f\n", acc_g[0], acc_g[1], acc_g[2]);
  Serial.printf("陀螺仪(dps):    X=%7.2f, Y=%7.2f, Z=%7.2f\n", gyr_dps[0], gyr_dps[1], gyr_dps[2]);
  Serial.println();
  
  delay(100);  // 100ms读取一次，对应100Hz采样率
}

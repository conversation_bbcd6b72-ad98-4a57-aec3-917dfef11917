/*
 * SC7I22 IMU Arduino测试程序
 * 通过调用原始驱动函数来读取IMU数据
 * 适用于ESP32-C3开发板
 */

// 定义Arduino平台标识，让原始驱动使用Arduino适配
#define ARDUINO

// 包含Arduino I2C适配器
#include "Arduino_I2C_Adapter.h"

// 包含原始驱动头文件
#include "SL_SC7I22_Driver/SL_SC7I22_Driver.h"

void setup() {
    Serial.begin(115200);
    delay(2000);

    Serial.println("=== SC7I22 IMU Arduino测试程序 ===");
    Serial.println("基于原始驱动函数库实现");
    Serial.println();

    // 初始化Arduino I2C适配器
    Arduino_I2C_Init();
    Serial.println();

    // 测试传感器连接
    Serial.println("测试传感器连接...");
    if (SL_SC7I22_Check()) {
        Serial.println("✓ 传感器连接正常");
    } else {
        Serial.println("✗ 传感器连接失败");
        Serial.println("请检查硬件连接");
        while(1) delay(1000);
    }

    // 调用原始驱动的初始化函数
    Serial.println("初始化传感器...");
    if (SL_SC7I22_Config()) {
        Serial.println("✓ SC7I22初始化成功！");
        Serial.println("传感器配置:");
        Serial.println("  - 加速度计: ±2G, 100Hz");
        Serial.println("  - 陀螺仪: ±2000dps, 100Hz");
        Serial.println("  - 工作模式: STREAM模式");
        Serial.println();
        Serial.println("开始读取IMU原始数据...");
        Serial.println();
    } else {
        Serial.println("✗ SC7I22初始化失败！");
        while (1) {
            delay(1000);
        }
    }
}

void loop() {
    signed short acc_data[3], gyr_data[3];

    // 调用原始驱动函数读取数据
    SL_SC7I22_RawData_Read(acc_data, gyr_data);

    // 输出原始数据
    Serial.printf("ACC: %6d, %6d, %6d | GYR: %6d, %6d, %6d\n",
                  acc_data[0], acc_data[1], acc_data[2],
                  gyr_data[0], gyr_data[1], gyr_data[2]);

    delay(200);  // 200ms读取一次
}



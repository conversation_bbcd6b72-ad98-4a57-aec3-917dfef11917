/*
 * SC7I22 IMU Arduino测试程序
 * 通过调用原始驱动函数来读取IMU数据
 * 适用于ESP32-C3开发板
 */

// 定义Arduino平台标识，让原始驱动使用Arduino适配
#define ARDUINO

// 包含Arduino I2C适配器
#include "Arduino_I2C_Adapter.h"

// 包含原始驱动头文件
#include "SL_SC7I22_Driver.h"

void setup() {
    Serial.begin(115200);
    delay(2000);

    Serial.println("=== SC7I22 IMU Arduino测试程序 ===");
    Serial.println("基于原始驱动函数库实现");
    Serial.println();

    // 初始化Arduino I2C适配器
    Arduino_I2C_Init();
    Serial.println();

    // 测试基本I2C通信
    Serial.println("测试基本I2C通信...");
    Wire.beginTransmission(0x19);
    uint8_t basic_result = Wire.endTransmission();
    Serial.printf("基本I2C测试结果: %d\n", basic_result);

    if (basic_result == 0) {
        Serial.println("✓ I2C地址0x19响应正常");
    } else {
        Serial.println("✗ I2C地址0x19无响应");
        Serial.println("尝试其他可能的配置...");

        // 尝试不同的I2C时钟频率
        uint32_t frequencies[] = {10000, 50000, 100000, 400000};
        for (int i = 0; i < 4; i++) {
            Wire.setClock(frequencies[i]);
            delay(50);
            Wire.beginTransmission(0x19);
            uint8_t test_result = Wire.endTransmission();
            Serial.printf("频率%dHz测试: %s\n", frequencies[i],
                         test_result == 0 ? "成功" : "失败");
            if (test_result == 0) break;
        }
    }

    // 测试传感器连接
    Serial.println("测试传感器连接...");
    if (SL_SC7I22_Check()) {
        Serial.println("✓ 传感器连接正常");
    } else {
        Serial.println("✗ 传感器连接失败");
        Serial.println("请检查硬件连接");
        while(1) delay(1000);
    }

    // 调用原始驱动的初始化函数
    Serial.println("初始化传感器...");
    if (SL_SC7I22_Config()) {
        Serial.println("✓ SC7I22初始化成功！");
        Serial.println("传感器配置:");
        Serial.println("  - 加速度计: ±2G, 100Hz");
        Serial.println("  - 陀螺仪: ±2000dps, 100Hz");
        Serial.println("  - 工作模式: STREAM模式");
        Serial.println();
        Serial.println("开始读取IMU原始数据...");
        Serial.println();
    } else {
        Serial.println("✗ SC7I22初始化失败！");
        while (1) {
            delay(1000);
        }
    }
}

void loop() {
    static int loop_count = 0;
    signed short acc_data[3], gyr_data[3];

    // 每10次循环输出一次详细诊断
    if (loop_count % 10 == 0) {
        Serial.println("=== 详细诊断信息 ===");

        // 重新检查传感器连接
        Serial.print("传感器连接检查: ");
        if (SL_SC7I22_Check()) {
            Serial.println("正常");
        } else {
            Serial.println("异常");
        }

        // 读取状态寄存器
        unsigned char status = 0;
        if (SL_SC7I22_I2c_Spi_Read(1, 0x0B, 1, &status)) {
            Serial.printf("状态寄存器(0x0B): 0x%02X\n", status);
            Serial.printf("  ACC数据就绪: %s\n", (status & 0x01) ? "是" : "否");
            Serial.printf("  GYR数据就绪: %s\n", (status & 0x02) ? "是" : "否");
        } else {
            Serial.println("无法读取状态寄存器");
        }

        // 读取配置寄存器验证
        unsigned char acc_conf, acc_range, gyr_conf, gyr_range;
        SL_SC7I22_I2c_Spi_Read(1, 0x40, 1, &acc_conf);
        SL_SC7I22_I2c_Spi_Read(1, 0x41, 1, &acc_range);
        SL_SC7I22_I2c_Spi_Read(1, 0x42, 1, &gyr_conf);
        SL_SC7I22_I2c_Spi_Read(1, 0x43, 1, &gyr_range);

        Serial.printf("配置寄存器: ACC_CONF=0x%02X, ACC_RANGE=0x%02X, GYR_CONF=0x%02X, GYR_RANGE=0x%02X\n",
                      acc_conf, acc_range, gyr_conf, gyr_range);
        Serial.println();
    }

    // 调用原始驱动函数读取数据
    SL_SC7I22_RawData_Read(acc_data, gyr_data);

    // 输出原始数据
    Serial.printf("ACC: %6d, %6d, %6d | GYR: %6d, %6d, %6d\n",
                  acc_data[0], acc_data[1], acc_data[2],
                  gyr_data[0], gyr_data[1], gyr_data[2]);

    loop_count++;
    delay(200);  // 200ms读取一次
}

// 手动重新配置传感器
void manualReconfigure() {
    Serial.println("=== 手动重新配置传感器 ===");

    // 软复位
    Serial.println("执行软复位...");
    SL_SC7I22_I2c_Spi_Write(1, 0x7D, 0x0E);
    delay(200);

    // 切换到bank 0
    Serial.println("切换到bank 0...");
    SL_SC7I22_I2c_Spi_Write(1, 0x7F, 0x00);
    delay(50);

    // 重新配置
    Serial.println("重新配置寄存器...");
    SL_SC7I22_I2c_Spi_Write(1, 0x40, 0x88);  // ACC_CONF
    SL_SC7I22_I2c_Spi_Write(1, 0x41, 0x01);  // ACC_RANGE
    SL_SC7I22_I2c_Spi_Write(1, 0x42, 0xC8);  // GYR_CONF
    SL_SC7I22_I2c_Spi_Write(1, 0x43, 0x00);  // GYR_RANGE
    SL_SC7I22_I2c_Spi_Write(1, 0x05, 0x50);  // I2C_CFG

    delay(300);
    Serial.println("重新配置完成");
}

// 手动I2C扫描函数（用于调试）
void manualI2CScan() {
    Serial.println("手动I2C扫描:");
    for (uint8_t addr = 0x08; addr <= 0x77; addr++) {
        Wire.beginTransmission(addr);
        uint8_t error = Wire.endTransmission();
        if (error == 0) {
            Serial.printf("发现设备: 0x%02X\n", addr);
        }
    }
}



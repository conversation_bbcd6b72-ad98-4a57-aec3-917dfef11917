/*
 * SC7I22 IMU Arduino测试程序
 * 通过调用原始驱动函数来读取IMU数据
 * 适用于ESP32-C3开发板
 */

// 定义Arduino平台标识，让原始驱动使用Arduino适配
#define ARDUINO

// 包含Arduino I2C适配器
#include "Arduino_I2C_Adapter.h"

// 包含原始驱动头文件
extern "C" {
    #include "SL_SC7I22_Driver/SL_SC7I22_Driver.h"
}

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== SC7I22 IMU Arduino测试程序 ===");
    Serial.println("基于原始驱动函数库实现");
    Serial.println("作者: Silan MEMS");
    Serial.println();
    
    // 初始化Arduino I2C适配器
    Arduino_I2C_Init();
    Serial.println();
    
    // 调用原始驱动的初始化函数
    Serial.println("正在初始化SC7I22传感器...");
    if (SL_SC7I22_Config()) {
        Serial.println("✓ SC7I22初始化成功！");
        Serial.println("传感器配置:");
        Serial.println("  - 加速度计: ±2G, 100Hz");
        Serial.println("  - 陀螺仪: ±2000dps, 100Hz");
        Serial.println("  - 工作模式: STREAM模式");
        Serial.println();
        Serial.println("开始读取IMU原始数据...");
        Serial.println();
    } else {
        Serial.println("✗ SC7I22初始化失败！");
        Serial.println("请检查:");
        Serial.println("  1. 硬件连接是否正确");
        Serial.println("  2. SDO引脚是否接到VDD");
        Serial.println("  3. 电源供电是否正常");
        while (1) {
            delay(1000);
        }
    }
}

void loop() {
    // 定义数据缓冲区
    signed short acc_data[3];  // 加速度计数据 [X, Y, Z]
    signed short gyr_data[3];  // 陀螺仪数据 [X, Y, Z]
    unsigned int timestamp;    // 传感器时间戳
    
    // 调用原始驱动函数读取原始数据
    SL_SC7I22_RawData_Read(acc_data, gyr_data);
    
    // 调用原始驱动函数读取时间戳
    timestamp = SL_SC7I22_TimeStamp_Read();
    
    // 转换为物理单位
    // 根据原始驱动配置: ±2G量程，±2000dps量程，16位分辨率
    float acc_scale = 2.0 / 32768.0;      // g/LSB
    float gyr_scale = 2000.0 / 32768.0;   // dps/LSB
    
    float acc_g[3], gyr_dps[3];
    for (int i = 0; i < 3; i++) {
        acc_g[i] = acc_data[i] * acc_scale;
        gyr_dps[i] = gyr_data[i] * gyr_scale;
    }
    
    // 输出数据
    Serial.println("=== SC7I22 IMU数据输出 ===");
    Serial.printf("时间戳: %lu\n", timestamp);
    Serial.println("原始数据:");
    Serial.printf("  加速度计: X=%6d, Y=%6d, Z=%6d\n", acc_data[0], acc_data[1], acc_data[2]);
    Serial.printf("  陀螺仪:   X=%6d, Y=%6d, Z=%6d\n", gyr_data[0], gyr_data[1], gyr_data[2]);
    Serial.println("物理单位:");
    Serial.printf("  加速度计(g):   X=%7.3f, Y=%7.3f, Z=%7.3f\n", acc_g[0], acc_g[1], acc_g[2]);
    Serial.printf("  陀螺仪(dps):   X=%7.2f, Y=%7.2f, Z=%7.2f\n", gyr_dps[0], gyr_dps[1], gyr_dps[2]);
    
    // 计算合成加速度（用于检验重力加速度）
    float acc_magnitude = sqrt(acc_g[0]*acc_g[0] + acc_g[1]*acc_g[1] + acc_g[2]*acc_g[2]);
    Serial.printf("  合成加速度: %.3f g\n", acc_magnitude);
    
    Serial.println();
    
    delay(100);  // 100ms读取一次，对应100Hz采样率
}

// 可选：添加一些测试函数

void printSensorInfo() {
    Serial.println("=== SC7I22传感器信息 ===");
    
    // 调用原始驱动的检查函数
    if (SL_SC7I22_Check()) {
        Serial.println("传感器连接: 正常");
    } else {
        Serial.println("传感器连接: 异常");
    }
    
    Serial.println("配置信息:");
    Serial.println("  制造商: Silan MEMS");
    Serial.println("  型号: SC7I22");
    Serial.println("  接口: I2C");
    Serial.printf("  地址: 0x%02X\n", SL_SC7I22_IIC_ADDRESS);
    Serial.println("  功能: 6轴IMU (3轴加速度计 + 3轴陀螺仪)");
}

void testSensorFunctions() {
    Serial.println("=== 测试传感器功能 ===");
    
    // 测试连接检查
    Serial.print("连接检查: ");
    if (SL_SC7I22_Check()) {
        Serial.println("通过");
    } else {
        Serial.println("失败");
    }
    
    // 测试时间戳读取
    unsigned int ts1 = SL_SC7I22_TimeStamp_Read();
    delay(10);
    unsigned int ts2 = SL_SC7I22_TimeStamp_Read();
    Serial.printf("时间戳测试: %lu -> %lu (差值: %lu)\n", ts1, ts2, ts2-ts1);
    
    // 测试数据读取
    signed short acc[3], gyr[3];
    SL_SC7I22_RawData_Read(acc, gyr);
    Serial.printf("数据读取测试: ACC[%d,%d,%d] GYR[%d,%d,%d]\n", 
                  acc[0], acc[1], acc[2], gyr[0], gyr[1], gyr[2]);
}

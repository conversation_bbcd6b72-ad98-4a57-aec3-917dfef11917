#include <Wire.h>

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 可能的I2C地址
#define SC7I22_ADDR_SDO_GND  0x18  // SDO接地
#define SC7I22_ADDR_SDO_VDD  0x19  // SDO接VDD

// 寄存器定义
#define SC7I22_WHO_AM_I      0x01
#define SC7I22_BANK_SELECT   0x7F

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== ESP32-C3 SC7I22 IMU 诊断工具 ===");
  Serial.println();
  
  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);  // 先用较低的时钟频率测试
  
  Serial.printf("I2C初始化完成 - SDA: GPIO%d, SCL: GPIO%d\n", SDA_PIN, SCL_PIN);
  Serial.println();
  
  // 扫描I2C总线
  scanI2CBus();
  
  // 测试两个可能的地址
  testSC7I22Address(SC7I22_ADDR_SDO_GND, "SDO接GND");
  testSC7I22Address(SC7I22_ADDR_SDO_VDD, "SDO接VDD");
  
  // 测试不同的I2C时钟频率
  testDifferentClockSpeeds();
}

void loop() {
  // 持续监控
  delay(5000);
  Serial.println("--- 5秒后重新检测 ---");
  testSC7I22Address(SC7I22_ADDR_SDO_VDD, "SDO接VDD");
}

void scanI2CBus() {
  Serial.println("扫描I2C总线...");
  Serial.println("地址范围: 0x08 - 0x77");
  
  int deviceCount = 0;
  for (uint8_t addr = 0x08; addr <= 0x77; addr++) {
    Wire.beginTransmission(addr);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.printf("发现I2C设备: 0x%02X\n", addr);
      deviceCount++;
    }
  }
  
  if (deviceCount == 0) {
    Serial.println("❌ 未发现任何I2C设备！");
    Serial.println("请检查：");
    Serial.println("1. SDA/SCL接线是否正确");
    Serial.println("2. 电源供电是否正常");
    Serial.println("3. 是否需要上拉电阻");
  } else {
    Serial.printf("✓ 总共发现 %d 个I2C设备\n", deviceCount);
  }
  Serial.println();
}

void testSC7I22Address(uint8_t addr, const char* description) {
  Serial.printf("测试SC7I22地址: 0x%02X (%s)\n", addr, description);
  
  // 测试基本通信
  Wire.beginTransmission(addr);
  uint8_t error = Wire.endTransmission();
  
  if (error != 0) {
    Serial.printf("❌ 地址0x%02X无响应 (错误代码: %d)\n", addr, error);
    printI2CError(error);
    Serial.println();
    return;
  }
  
  Serial.printf("✓ 地址0x%02X有响应\n", addr);
  
  // 切换到bank 0
  if (!writeRegister(addr, SC7I22_BANK_SELECT, 0x00)) {
    Serial.println("❌ 无法写入BANK_SELECT寄存器");
    Serial.println();
    return;
  }
  
  // 读取WHO_AM_I寄存器
  uint8_t who_am_i = 0;
  if (readRegister(addr, SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.printf("WHO_AM_I寄存器: 0x%02X ", who_am_i);
    if (who_am_i == 0x6A) {
      Serial.println("✓ SC7I22识别成功！");
    } else {
      Serial.println("❌ WHO_AM_I值不正确（期望0x6A）");
    }
  } else {
    Serial.println("❌ 无法读取WHO_AM_I寄存器");
  }
  
  // 测试其他寄存器
  testOtherRegisters(addr);
  Serial.println();
}

bool writeRegister(uint8_t addr, uint8_t reg, uint8_t data) {
  Wire.beginTransmission(addr);
  Wire.write(reg);
  Wire.write(data);
  return (Wire.endTransmission() == 0);
}

bool readRegister(uint8_t addr, uint8_t reg, uint8_t* data, uint8_t len) {
  Wire.beginTransmission(addr);
  Wire.write(reg);
  if (Wire.endTransmission() != 0) return false;
  
  Wire.requestFrom(addr, len);
  for (int i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      return false;
    }
  }
  return true;
}

void testOtherRegisters(uint8_t addr) {
  uint8_t test_regs[] = {0x0B, 0x40, 0x41, 0x42, 0x43};  // 状态和配置寄存器
  const char* reg_names[] = {"STATUS", "ACC_CONF", "ACC_RANGE", "GYR_CONF", "GYR_RANGE"};
  
  for (int i = 0; i < 5; i++) {
    uint8_t value = 0;
    if (readRegister(addr, test_regs[i], &value, 1)) {
      Serial.printf("%s(0x%02X): 0x%02X  ", reg_names[i], test_regs[i], value);
    } else {
      Serial.printf("%s(0x%02X): 读取失败  ", reg_names[i], test_regs[i]);
    }
  }
  Serial.println();
}

void testDifferentClockSpeeds() {
  Serial.println("测试不同I2C时钟频率:");
  
  uint32_t speeds[] = {100000, 400000, 1000000};  // 100kHz, 400kHz, 1MHz
  const char* speed_names[] = {"100kHz", "400kHz", "1MHz"};
  
  for (int i = 0; i < 3; i++) {
    Serial.printf("测试 %s: ", speed_names[i]);
    Wire.setClock(speeds[i]);
    delay(10);
    
    uint8_t who_am_i = 0;
    writeRegister(SC7I22_ADDR_SDO_VDD, SC7I22_BANK_SELECT, 0x00);
    if (readRegister(SC7I22_ADDR_SDO_VDD, SC7I22_WHO_AM_I, &who_am_i, 1)) {
      if (who_am_i == 0x6A) {
        Serial.println("✓ 通信正常");
      } else {
        Serial.printf("❌ WHO_AM_I错误(0x%02X)\n", who_am_i);
      }
    } else {
      Serial.println("❌ 通信失败");
    }
  }
  
  // 恢复到400kHz
  Wire.setClock(400000);
  Serial.println();
}

void printI2CError(uint8_t error) {
  switch (error) {
    case 1:
      Serial.println("  错误1: 数据太长，超出传输缓冲区");
      break;
    case 2:
      Serial.println("  错误2: 在传输地址时收到NACK");
      Serial.println("  → 设备地址可能错误或设备未连接");
      break;
    case 3:
      Serial.println("  错误3: 在传输数据时收到NACK");
      break;
    case 4:
      Serial.println("  错误4: 其他错误");
      break;
    case 5:
      Serial.println("  错误5: 超时");
      break;
    default:
      Serial.printf("  未知错误代码: %d\n", error);
      break;
  }
}

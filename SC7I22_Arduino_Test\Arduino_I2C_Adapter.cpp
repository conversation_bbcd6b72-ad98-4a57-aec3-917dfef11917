/*
 * Arduino I2C适配器实现文件
 * 为原始SC7I22驱动提供Arduino平台的I2C接口实现
 */

#include "Arduino_I2C_Adapter.h"
#include <stdarg.h>

// 从原始驱动头文件获取I2C地址配置
#define SL_SC7I22_SDO_VDD_GND            1
#define SL_SC7I22_IIC_7BITS_8BITS        0

#if SL_SC7I22_SDO_VDD_GND==0
#define SL_SC7I22_IIC_7BITS_ADDR        0x18
#define SL_SC7I22_IIC_8BITS_WRITE_ADDR  0x30
#define SL_SC7I22_IIC_8BITS_READ_ADDR   0x31
#else
#define SL_SC7I22_IIC_7BITS_ADDR        0x19
#define SL_SC7I22_IIC_8BITS_WRITE_ADDR  0x32
#define SL_SC7I22_IIC_8BITS_READ_ADDR   0x33
#endif

#if SL_SC7I22_IIC_7BITS_8BITS==0
#define SL_SC7I22_IIC_ADDRESS        SL_SC7I22_IIC_7BITS_ADDR
#else
#define SL_SC7I22_IIC_WRITE_ADDRESS  SL_SC7I22_IIC_8BITS_WRITE_ADDR
#define SL_SC7I22_IIC_READ_ADDRESS   SL_SC7I22_IIC_8BITS_READ_ADDR
#endif

// 初始化Arduino I2C适配器
void Arduino_I2C_Init(void) {
    Wire.begin(SDA_PIN, SCL_PIN);
    Wire.setClock(50000);   // 降低到50kHz，提高稳定性
    Wire.setTimeout(1000);  // 设置1秒超时
    delay(200);             // 增加初始化延时

    Serial.printf("Arduino I2C适配器初始化完成\n");
    Serial.printf("SDA: GPIO%d, SCL: GPIO%d\n", SDA_PIN, SCL_PIN);
    Serial.printf("I2C地址: 0x%02X\n", SL_SC7I22_IIC_ADDRESS);
    Serial.printf("I2C时钟: 50kHz\n");

    // 测试I2C总线
    Serial.println("扫描I2C总线...");
    int deviceCount = 0;
    for (uint8_t addr = 0x08; addr <= 0x77; addr++) {
        Wire.beginTransmission(addr);
        if (Wire.endTransmission() == 0) {
            Serial.printf("发现设备: 0x%02X\n", addr);
            deviceCount++;
        }
    }
    Serial.printf("总共发现 %d 个I2C设备\n", deviceCount);
}

// 实现原始驱动要求的I2C写入函数
extern "C" unsigned char SL_SC7I22_I2c_Spi_Write(unsigned char sl_spi_iic, unsigned char reg, unsigned char dat) {
    if (sl_spi_iic == 1) {  // I2C模式
        // 尝试3次写入
        for (int retry = 0; retry < 3; retry++) {
            Wire.beginTransmission(SL_SC7I22_IIC_ADDRESS);
            Wire.write(reg);
            Wire.write(dat);
            uint8_t result = Wire.endTransmission();

            if (result == 0) {
                if (retry > 0) {
                    Serial.printf("I2C写入成功(重试%d次): 寄存器0x%02X, 数据0x%02X\n", retry, reg, dat);
                }
                return 1;
            }

            if (retry < 2) {  // 不是最后一次重试
                delay(10);    // 重试前等待
            } else {
                Serial.printf("I2C写入失败: 寄存器0x%02X, 数据0x%02X, 错误码%d\n", reg, dat, result);
            }
        }
        return 0;
    }
    return 0;  // 不支持SPI
}

// 实现原始驱动要求的I2C读取函数
extern "C" unsigned char SL_SC7I22_I2c_Spi_Read(unsigned char sl_spi_iic, unsigned char reg, unsigned short len, unsigned char* buf) {
    if (sl_spi_iic == 1) {  // I2C模式
        // 尝试3次读取
        for (int retry = 0; retry < 3; retry++) {
            Wire.beginTransmission(SL_SC7I22_IIC_ADDRESS);
            Wire.write(reg);
            uint8_t result = Wire.endTransmission(false);  // 使用repeated start

            if (result != 0) {
                if (retry < 2) {
                    delay(10);
                    continue;
                } else {
                    Serial.printf("I2C读取请求失败: 寄存器0x%02X, 错误码%d\n", reg, result);
                    return 0;
                }
            }

            Wire.requestFrom(SL_SC7I22_IIC_ADDRESS, (uint8_t)len, (uint8_t)true);  // stop=true

            int received = 0;
            for (int i = 0; i < len; i++) {
                if (Wire.available()) {
                    buf[i] = Wire.read();
                    received++;
                } else {
                    break;
                }
            }

            if (received == len) {
                if (retry > 0) {
                    Serial.printf("I2C读取成功(重试%d次): 寄存器0x%02X\n", retry, reg);
                }
                return 1;
            }

            if (retry < 2) {
                delay(10);
            } else {
                Serial.printf("I2C读取数据不足: 寄存器0x%02X, 期望%d字节, 实际%d字节\n", reg, len, received);
            }
        }
        return 0;
    }
    return 0;  // 不支持SPI
}

// 调试输出函数（替代原始驱动中的USART_printf）
void Debug_Printf(const char* format, ...) {
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    Serial.print(buffer);
}

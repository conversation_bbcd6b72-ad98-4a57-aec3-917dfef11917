/*
 * Arduino I2C适配器头文件
 * 为原始SC7I22驱动提供Arduino平台的I2C接口实现
 */

#ifndef ARDUINO_I2C_ADAPTER_H
#define ARDUINO_I2C_ADAPTER_H

#include <Arduino.h>
#include <Wire.h>

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 初始化Arduino I2C适配器
void Arduino_I2C_Init(void);

// 为原始驱动提供的I2C接口函数
extern "C" {
    unsigned char SL_SC7I22_I2c_Spi_Write(unsigned char sl_spi_iic, unsigned char reg, unsigned char dat);
    unsigned char SL_SC7I22_I2c_Spi_Read(unsigned char sl_spi_iic, unsigned char reg, unsigned short len, unsigned char* buf);
}

// 调试输出函数（替代原始驱动中的USART_printf）
void Debug_Printf(const char* format, ...);

#endif

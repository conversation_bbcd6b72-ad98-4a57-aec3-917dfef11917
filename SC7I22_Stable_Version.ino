#include <Wire.h>

// SC7I22 IMU配置参数
#define SC7I22_IIC_ADDR        0x19    // 根据诊断结果，使用0x19地址

// 寄存器地址定义
#define SC7I22_WHO_AM_I         0x01
#define SC7I22_STATUS_REG       0x0B
#define SC7I22_ACC_DATA_START   0x0C
#define SC7I22_SOFT_RESET       0x7D
#define SC7I22_ACC_CONF         0x40
#define SC7I22_ACC_RANGE        0x41
#define SC7I22_GYR_CONF         0x42
#define SC7I22_GYR_RANGE        0x43
#define SC7I22_I2C_CFG          0x05
#define SC7I22_BANK_SELECT      0x7F

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// I2C写入函数（带重试机制）
bool SC7I22_WriteReg(uint8_t reg, uint8_t data, int max_retries = 3) {
  for (int retry = 0; retry < max_retries; retry++) {
    Wire.beginTransmission(SC7I22_IIC_ADDR);
    Wire.write(reg);
    Wire.write(data);
    uint8_t result = Wire.endTransmission();
    
    if (result == 0) {
      if (retry > 0) {
        Serial.printf("写入寄存器0x%02X成功 (重试%d次)\n", reg, retry);
      }
      return true;
    }
    
    Serial.printf("写入寄存器0x%02X失败，错误代码: %d (尝试%d/%d)\n", reg, result, retry+1, max_retries);
    delay(10);  // 重试前等待
  }
  return false;
}

// I2C读取函数（带重试机制）
bool SC7I22_ReadReg(uint8_t reg, uint8_t* data, uint8_t len, int max_retries = 3) {
  for (int retry = 0; retry < max_retries; retry++) {
    Wire.beginTransmission(SC7I22_IIC_ADDR);
    Wire.write(reg);
    uint8_t result = Wire.endTransmission();
    
    if (result != 0) {
      Serial.printf("请求寄存器0x%02X失败，错误代码: %d (尝试%d/%d)\n", reg, result, retry+1, max_retries);
      delay(10);
      continue;
    }
    
    Wire.requestFrom(SC7I22_IIC_ADDR, len);
    uint8_t received = 0;
    for (int i = 0; i < len; i++) {
      if (Wire.available()) {
        data[i] = Wire.read();
        received++;
      } else {
        break;
      }
    }
    
    if (received == len) {
      if (retry > 0) {
        Serial.printf("读取寄存器0x%02X成功 (重试%d次)\n", reg, retry);
      }
      return true;
    }
    
    Serial.printf("读取寄存器0x%02X数据不足，期望%d字节，实际%d字节 (尝试%d/%d)\n", 
                  reg, len, received, retry+1, max_retries);
    delay(10);
  }
  return false;
}

// 简化的连接检查（不需要切换bank）
bool SC7I22_Check() {
  uint8_t who_am_i = 0;
  
  Serial.printf("检查SC7I22连接 (地址: 0x19)...\n");
  
  // 直接读取WHO_AM_I，不切换bank
  if (SC7I22_ReadReg(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.printf("WHO_AM_I寄存器: 0x%02X ", who_am_i);
    if (who_am_i == 0x6A) {
      Serial.println("✓ SC7I22识别成功！");
      return true;
    } else {
      Serial.println("❌ WHO_AM_I值不正确（期望0x6A）");
      // 尝试切换bank后再读取
      Serial.println("尝试切换bank后重新读取...");
      if (SC7I22_WriteReg(SC7I22_BANK_SELECT, 0x00)) {
        delay(20);
        if (SC7I22_ReadReg(SC7I22_WHO_AM_I, &who_am_i, 1)) {
          Serial.printf("切换bank后WHO_AM_I: 0x%02X ", who_am_i);
          if (who_am_i == 0x6A) {
            Serial.println("✓ SC7I22识别成功！");
            return true;
          }
        }
      }
      return false;
    }
  } else {
    Serial.println("❌ 无法读取WHO_AM_I寄存器");
    return false;
  }
}

// SC7I22初始化配置
bool SC7I22_Config() {
  if (!SC7I22_Check()) {
    return false;
  }
  
  Serial.println("开始配置SC7I22...");
  
  // 确保在bank 0
  Serial.println("确保在Bank 0...");
  SC7I22_WriteReg(SC7I22_BANK_SELECT, 0x00);
  delay(50);
  
  // 软复位，进入正常工作模式
  Serial.println("执行软复位...");
  if (!SC7I22_WriteReg(SC7I22_SOFT_RESET, 0x0E)) {
    Serial.println("软复位失败，尝试继续配置...");
  }
  delay(200);  // 增加复位后等待时间
  
  // 重新确保在bank 0
  SC7I22_WriteReg(SC7I22_BANK_SELECT, 0x00);
  delay(50);
  
  // 配置加速度计：100Hz采样率
  Serial.println("配置加速度计...");
  if (!SC7I22_WriteReg(SC7I22_ACC_CONF, 0x88)) {
    Serial.println("⚠️ 加速度计配置可能失败");
  }
  
  // 配置加速度计量程：±2G
  if (!SC7I22_WriteReg(SC7I22_ACC_RANGE, 0x01)) {
    Serial.println("⚠️ 加速度计量程配置可能失败");
  }
  
  // 配置陀螺仪：100Hz采样率
  Serial.println("配置陀螺仪...");
  if (!SC7I22_WriteReg(SC7I22_GYR_CONF, 0xC8)) {
    Serial.println("⚠️ 陀螺仪配置可能失败");
  }
  
  // 配置陀螺仪量程：±2000dps
  if (!SC7I22_WriteReg(SC7I22_GYR_RANGE, 0x00)) {
    Serial.println("⚠️ 陀螺仪量程配置可能失败");
  }
  
  // I2C接口配置
  Serial.println("配置I2C接口...");
  if (!SC7I22_WriteReg(SC7I22_I2C_CFG, 0x50)) {
    Serial.println("⚠️ I2C接口配置可能失败");
  }
  
  // 等待传感器稳定
  delay(300);
  
  Serial.println("✓ SC7I22配置完成！");
  return true;
}

// 读取原始数据（简化版本）
bool SC7I22_ReadData(int16_t acc[3], int16_t gyr[3]) {
  uint8_t raw_data[12];
  
  // 直接读取数据，不等待状态寄存器
  if (!SC7I22_ReadReg(SC7I22_ACC_DATA_START, raw_data, 12)) {
    return false;
  }
  
  // 解析数据
  acc[0] = (int16_t)((raw_data[0] << 8) | raw_data[1]);   // ACCX
  acc[1] = (int16_t)((raw_data[2] << 8) | raw_data[3]);   // ACCY
  acc[2] = (int16_t)((raw_data[4] << 8) | raw_data[5]);   // ACCZ
  
  gyr[0] = (int16_t)((raw_data[6] << 8) | raw_data[7]);   // GYRX
  gyr[1] = (int16_t)((raw_data[8] << 8) | raw_data[9]);   // GYRY
  gyr[2] = (int16_t)((raw_data[10] << 8) | raw_data[11]); // GYRZ
  
  return true;
}

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("ESP32-C3 SC7I22 IMU 稳定版本");
  Serial.println("============================");
  
  // 初始化I2C，使用更低的时钟频率
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(50000);   // 50kHz，非常保守的时钟频率
  delay(200);
  
  Serial.println("I2C初始化完成，时钟频率: 50kHz");
  
  // 初始化SC7I22
  if (SC7I22_Config()) {
    Serial.println("🎉 SC7I22初始化成功，开始读取数据...");
    Serial.println();
    
    // 初始化成功后，可以尝试提高时钟频率
    Wire.setClock(100000);  // 提升到100kHz
    Serial.println("I2C时钟频率提升到100kHz");
    
  } else {
    Serial.println("❌ SC7I22初始化失败！");
    Serial.println("尝试基本通信测试...");
    
    // 基本通信测试
    uint8_t test_data = 0;
    if (SC7I22_ReadReg(SC7I22_WHO_AM_I, &test_data, 1)) {
      Serial.printf("基本读取成功，WHO_AM_I: 0x%02X\n", test_data);
      Serial.println("传感器存在但配置可能有问题，尝试读取原始数据...");
    } else {
      Serial.println("基本通信也失败，请检查硬件连接");
      while(1) delay(1000);
    }
  }
}

void loop() {
  int16_t acc_data[3], gyr_data[3];
  
  // 读取IMU数据
  if (SC7I22_ReadData(acc_data, gyr_data)) {
    // 转换为物理单位
    float acc_scale = 2.0 / 32768.0;      // ±2G量程
    float gyr_scale = 2000.0 / 32768.0;   // ±2000dps量程
    
    float acc_g_x = acc_data[0] * acc_scale;
    float acc_g_y = acc_data[1] * acc_scale;
    float acc_g_z = acc_data[2] * acc_scale;
    
    float gyr_dps_x = gyr_data[0] * gyr_scale;
    float gyr_dps_y = gyr_data[1] * gyr_scale;
    float gyr_dps_z = gyr_data[2] * gyr_scale;
    
    // 打印数据
    Serial.printf("ACC(g): X=%6.3f, Y=%6.3f, Z=%6.3f | ", acc_g_x, acc_g_y, acc_g_z);
    Serial.printf("GYR(dps): X=%7.2f, Y=%7.2f, Z=%7.2f\n", gyr_dps_x, gyr_dps_y, gyr_dps_z);
    
  } else {
    Serial.println("❌ 读取数据失败");
  }
  
  delay(200);  // 200ms读取一次，降低频率减少I2C负载
}

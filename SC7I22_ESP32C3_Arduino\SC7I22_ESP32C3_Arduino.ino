#include <Wire.h>

// SC7I22 IMU配置参数
#define SC7I22_SDO_VDD_GND            0    // SDO引脚接电源
#define SC7I22_IIC_7BITS_8BITS        0    // 使用7位I2C地址
#define SC7I22_FIFO_MODE_ENABLE       0    // 使用STREAM模式，不使用FIFO

// I2C地址配置
#if SC7I22_SDO_VDD_GND == 0
  #define SC7I22_IIC_ADDR        0x18
#else
  #define SC7I22_IIC_ADDR        0x19
#endif

// 寄存器地址定义
#define SC7I22_WHO_AM_I         0x01
#define SC7I22_STATUS_REG       0x0B
#define SC7I22_ACC_DATA_START   0x0C
#define SC7I22_SOFT_RESET       0x7D
#define SC7I22_ACC_CONF         0x40
#define SC7I22_ACC_RANGE        0x41
#define SC7I22_GYR_CONF         0x42
#define SC7I22_GYR_RANGE        0x43
#define SC7I22_I2C_CFG          0x05

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 数据结构
struct IMU_Data {
  int16_t acc_x, acc_y, acc_z;    // 加速度计数据
  int16_t gyr_x, gyr_y, gyr_z;    // 陀螺仪数据
  float acc_g_x, acc_g_y, acc_g_z; // 加速度计数据(g)
  float gyr_dps_x, gyr_dps_y, gyr_dps_z; // 陀螺仪数据(度/秒)
};

IMU_Data imu_data;

// I2C写入函数
bool SC7I22_WriteReg(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(SC7I22_IIC_ADDR);
  Wire.write(reg);
  Wire.write(data);
  return (Wire.endTransmission() == 0);
}

// I2C读取函数
bool SC7I22_ReadReg(uint8_t reg, uint8_t* data, uint8_t len) {
  Wire.beginTransmission(SC7I22_IIC_ADDR);
  Wire.write(reg);
  if (Wire.endTransmission() != 0) return false;
  
  Wire.requestFrom(SC7I22_IIC_ADDR, len);
  for (int i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      return false;
    }
  }
  return true;
}

// SC7I22连接检查
bool SC7I22_Check() {
  uint8_t who_am_i = 0;
  
  // 切换到bank 0
  SC7I22_WriteReg(0x7F, 0x00);
  
  if (SC7I22_ReadReg(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.printf("WHO_AM_I: 0x%02X\n", who_am_i);
    return (who_am_i == 0x6A);
  }
  return false;
}

// SC7I22初始化配置
bool SC7I22_Init() {
  if (!SC7I22_Check()) {
    Serial.println("SC7I22检测失败！");
    return false;
  }
  
  Serial.println("SC7I22检测成功，开始初始化...");
  
  // 软复位，进入正常工作模式
  SC7I22_WriteReg(SC7I22_SOFT_RESET, 0x0E);
  delay(100);
  
  // 配置加速度计：100Hz采样率
  SC7I22_WriteReg(SC7I22_ACC_CONF, 0x88);
  
  // 配置加速度计量程：±2G
  SC7I22_WriteReg(SC7I22_ACC_RANGE, 0x01);
  
  // 配置陀螺仪：100Hz采样率
  SC7I22_WriteReg(SC7I22_GYR_CONF, 0xC8);
  
  // 配置陀螺仪量程：±2000dps
  SC7I22_WriteReg(SC7I22_GYR_RANGE, 0x00);
  
  // I2C接口配置
  SC7I22_WriteReg(SC7I22_I2C_CFG, 0x50);
  
  // 等待传感器稳定
  delay(100);
  
  Serial.println("SC7I22初始化完成！");
  return true;
}

// 读取原始数据
bool SC7I22_ReadRawData() {
  uint8_t raw_data[12];
  uint8_t status = 0;
  uint16_t timeout = 0;
  
  // 等待数据就绪
  while ((status & 0x03) != 0x03) {
    if (!SC7I22_ReadReg(SC7I22_STATUS_REG, &status, 1)) {
      return false;
    }
    timeout++;
    if (timeout > 1000) {
      Serial.println("数据就绪超时！");
      return false;
    }
    delay(1);
  }
  
  // 读取12字节原始数据
  if (!SC7I22_ReadReg(SC7I22_ACC_DATA_START, raw_data, 12)) {
    return false;
  }
  
  // 解析加速度计数据（高字节在前）
  imu_data.acc_x = (int16_t)((raw_data[0] << 8) | raw_data[1]);
  imu_data.acc_y = (int16_t)((raw_data[2] << 8) | raw_data[3]);
  imu_data.acc_z = (int16_t)((raw_data[4] << 8) | raw_data[5]);
  
  // 解析陀螺仪数据（高字节在前）
  imu_data.gyr_x = (int16_t)((raw_data[6] << 8) | raw_data[7]);
  imu_data.gyr_y = (int16_t)((raw_data[8] << 8) | raw_data[9]);
  imu_data.gyr_z = (int16_t)((raw_data[10] << 8) | raw_data[11]);
  
  // 转换为物理单位
  // 加速度计：±2G量程，16位分辨率
  float acc_scale = 2.0 / 32768.0;  // g/LSB
  imu_data.acc_g_x = imu_data.acc_x * acc_scale;
  imu_data.acc_g_y = imu_data.acc_y * acc_scale;
  imu_data.acc_g_z = imu_data.acc_z * acc_scale;
  
  // 陀螺仪：±2000dps量程，16位分辨率
  float gyr_scale = 2000.0 / 32768.0;  // dps/LSB
  imu_data.gyr_dps_x = imu_data.gyr_x * gyr_scale;
  imu_data.gyr_dps_y = imu_data.gyr_y * gyr_scale;
  imu_data.gyr_dps_z = imu_data.gyr_z * gyr_scale;
  
  return true;
}

// 打印IMU数据
void PrintIMUData() {
  Serial.println("=== SC7I22 IMU数据 ===");
  Serial.printf("加速度计 (g): X=%.3f, Y=%.3f, Z=%.3f\n", 
                imu_data.acc_g_x, imu_data.acc_g_y, imu_data.acc_g_z);
  Serial.printf("陀螺仪 (dps): X=%.2f, Y=%.2f, Z=%.2f\n", 
                imu_data.gyr_dps_x, imu_data.gyr_dps_y, imu_data.gyr_dps_z);
  Serial.printf("原始数据 - ACC: %d,%d,%d  GYR: %d,%d,%d\n",
                imu_data.acc_x, imu_data.acc_y, imu_data.acc_z,
                imu_data.gyr_x, imu_data.gyr_y, imu_data.gyr_z);
  Serial.println();
}

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("ESP32-C3 SC7I22 IMU测试开始");
  
  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000);  // 400kHz I2C时钟
  
  // 初始化SC7I22
  if (SC7I22_Init()) {
    Serial.println("SC7I22初始化成功！");
  } else {
    Serial.println("SC7I22初始化失败！");
    while(1) {
      delay(1000);
    }
  }
  
  delay(1000);
}

void loop() {
  // 读取IMU数据
  if (SC7I22_ReadRawData()) {
    PrintIMUData();
  } else {
    Serial.println("读取IMU数据失败！");
  }
  
  delay(100);  // 100ms读取一次，对应100Hz采样率
}

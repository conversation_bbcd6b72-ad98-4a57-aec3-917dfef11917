
/*
Copyright (c) 2023 Silan MEMS. All Rights Reserved.
*/
#ifndef __SL_SC7I22_DRIVER_H__
#define __SL_SC7I22_DRIVER_H__

#ifdef __cplusplus
extern "C" {
#endif
/********包含相关IIC头文件******************/
//#include "i2c.h"
#ifdef ARDUINO
// Arduino平台适配
#else
// 原始平台
#endif
/***************************************/

//调试信息源码打印输出开关
#define SL_Sensor_Algo_Release_Enable  0x01
//是否启用FIFO缓存模式，默认STREAM模式
#define SL_SC7I22_FIFO_MODE_ENABLE    0x00


/***使用驱动前请根据实际硬件连接配置******/
/**SC7I22的SDO引脚接地：  0****************/
/**SC7I22的SDO引脚接电源：1****************/
#define SL_SC7I22_SDO_VDD_GND            1
/*****************************************/
/***使用驱动前请根据实际IIC地址位数配置***/
/**SC7I22的IIC接口地址配置 7bits：  0****/
/**SC7I22的IIC接口地址配置 8bits：  1****/
#define SL_SC7I22_IIC_7BITS_8BITS        0
/*****************************************/
#if SL_SC7I22_SDO_VDD_GND==0
#define SL_SC7I22_IIC_7BITS_ADDR        0x18
#define SL_SC7I22_IIC_8BITS_WRITE_ADDR  0x30
#define SL_SC7I22_IIC_8BITS_READ_ADDR   0x31
#else
#define SL_SC7I22_IIC_7BITS_ADDR        0x19
#define SL_SC7I22_IIC_8BITS_WRITE_ADDR  0x32
#define SL_SC7I22_IIC_8BITS_READ_ADDR   0x33
#endif
#if SL_SC7I22_IIC_7BITS_8BITS==0
#define SL_SC7I22_IIC_ADDRESS        SL_SC7I22_IIC_7BITS_ADDR
#else
#define SL_SC7I22_IIC_WRITE_ADDRESS  SL_SC7I22_IIC_8BITS_WRITE_ADDR
#define SL_SC7I22_IIC_READ_ADDRESS   SL_SC7I22_IIC_8BITS_READ_ADDR
#endif

/********客户需要实现IIC/SPI接口方法****************/
extern unsigned char SL_SC7I22_I2c_Spi_Write(unsigned char sl_spi_iic, unsigned char reg, unsigned char dat);
extern unsigned char SL_SC7I22_I2c_Spi_Read(unsigned char sl_spi_iic, unsigned char reg, unsigned short len, unsigned char* buf);
/**SL_SC7I22_I2c_Spi_Write 参数说明： sl_spi_iic:0=spi  1=i2c  Reg：寄存器地址   dat：寄存器配置数值******************/
/**SL_SC7I22_I2c_Spi_Write 返回值 是一个表示写入的函数******************************************************************/
/***SL_SC7I22_I2c_Spi_Read 参数说明： sl_spi_iic:0=spi  1=i2c Reg 同上，len:读取数据长度，buf:存储数据首地址（指针）***/
/***SL_SC7I22_I2c_Spi_Read 返回值 是可以进行的调用读取数据的函数************************************************/

/*************I2C通讯检测函数******************/
unsigned char SL_SC7I22_Check(void);
/*************函数返回值说明*****************/
/**return : 1   IIC通讯正常IC正常**************/
/**return : 0   IIC通讯异常或IC不正常**********/



/*************传感器初始化函数*******************/
unsigned char SL_SC7I22_Config(void);
/*************函数返回值说明*****************/
/**return : 1    IIC通讯正常IC正常*************/
/**return : 0;   IIC通讯异常或IC不正常*********/

/*************SC7I22 Sensor Time**************/
unsigned int SL_SC7I22_TimeStamp_Read(void);
/*************函数返回值说明*****************/
/**return : Internal Sensor Time***************/

#if SL_SC7I22_FIFO_MODE_ENABLE ==0x00
/******实时读取数据寄存器中数据，相当于是从400Hz的FIFO缓存中取出数据******/
void SL_SC7I22_RawData_Read(signed short* acc_data_buf, signed short* gyr_data_buf);
/************* 传入XYZ轴数据变量的地址*****************/
/************* *acc_data_buf:    ACC数组指针***********************/
/************* *gyr_data_buf:    GYR数组指针***********************/

#else
/******实时读取数据寄存器的FIFO数据******/
unsigned short SC7I22_FIFO_Read(signed short* accx_buf, signed short* accy_buf, signed short* accz_buf, signed short* gyrx_buf, signed short* gyry_buf, signed short* gyrz_buf);
/*************传入XYZ轴数据首地址**************************/
/*************accx_buf[0]:    X轴第一个数据****************/
/*************accy_buf[0]:    Y轴第一个数据****************/
/*************accz_buf[0]:    Z轴第一个数据****************/
/*************gyrx_buf[0]:    X轴第一个数据****************/
/*************gyry_buf[0]:    Y轴第一个数据****************/
/*************gyrz_buf[0]:    Z轴第一个数据****************/

/****************函数返回值说明****************************/
/**return : len         表示数组长度*************************/
#endif


/*********传感器进入关闭模式*************/
unsigned char SL_SC7I22_POWER_DOWN(void);
/**0: 关闭模式失败***********************/
/**1: 关闭模式成功***********************/

/**reg map***************/
#define SC7I22_WHO_AM_I         0x01


#ifdef __cplusplus
}
#endif

#endif // SC7I22


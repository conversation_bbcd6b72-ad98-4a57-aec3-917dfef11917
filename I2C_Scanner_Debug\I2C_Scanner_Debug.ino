#include <Wire.h>

// ESP32-C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== ESP32-C3 I2C总线全面诊断 ===");
  Serial.println();
  
  // 测试不同的I2C引脚组合
  testI2CPins();
}

void loop() {
  delay(10000);
  Serial.println("\n=== 10秒后重新扫描 ===");
  scanI2CDevices(SDA_PIN, SCL_PIN, 100000);
}

void testI2CPins() {
  Serial.println("1. 测试I2C引脚配置");
  
  // ESP32-C3常用的I2C引脚组合
  int pin_combinations[][2] = {
    {8, 9},   // 默认
    {4, 5},   // 备选1
    {6, 7},   // 备选2
    {0, 1},   // 备选3
    {2, 3},   // 备选4
    {10, 8},  // 备选5
  };
  
  const char* pin_names[] = {
    "GPIO8(SDA), GPIO9(SCL) - 默认",
    "GPIO4(SDA), GPIO5(SCL)",
    "GPIO6(SDA), GPIO7(SCL)", 
    "GPIO0(SDA), GPIO1(SCL)",
    "GPIO2(SDA), GPIO3(SCL)",
    "GPIO10(SDA), GPIO8(SCL)"
  };
  
  for (int i = 0; i < 6; i++) {
    Serial.printf("\n测试引脚组合 %d: %s\n", i+1, pin_names[i]);
    scanI2CDevices(pin_combinations[i][0], pin_combinations[i][1], 100000);
    delay(500);
  }
}

void scanI2CDevices(int sda, int scl, uint32_t freq) {
  // 重新初始化I2C
  Wire.end();
  delay(100);
  Wire.begin(sda, scl);
  Wire.setClock(freq);
  delay(100);
  
  Serial.printf("扫描 SDA=%d, SCL=%d, 频率=%dHz\n", sda, scl, freq);
  
  int deviceCount = 0;
  bool foundSC7I22 = false;
  
  // 扫描所有可能的7位地址
  for (uint8_t addr = 0x08; addr <= 0x77; addr++) {
    Wire.beginTransmission(addr);
    uint8_t error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.printf("  ✓ 发现设备: 0x%02X", addr);
      deviceCount++;
      
      // 检查是否可能是SC7I22
      if (addr == 0x18 || addr == 0x19) {
        Serial.print(" (可能是SC7I22!)");
        foundSC7I22 = true;
        
        // 尝试读取WHO_AM_I
        testWhoAmI(addr);
      }
      Serial.println();
    }
  }
  
  if (deviceCount == 0) {
    Serial.println("  ❌ 未发现任何设备");
    Serial.println("  可能原因:");
    Serial.println("    - 接线错误");
    Serial.println("    - 电源问题");
    Serial.println("    - 需要上拉电阻");
    Serial.println("    - 设备损坏");
  } else {
    Serial.printf("  总计: %d个设备\n", deviceCount);
    if (!foundSC7I22) {
      Serial.println("  ⚠️  未发现SC7I22 (地址0x18或0x19)");
    }
  }
}

void testWhoAmI(uint8_t addr) {
  // 尝试不同的bank切换方式
  uint8_t bank_commands[] = {0x00, 0x80};
  
  for (int i = 0; i < 2; i++) {
    // 切换bank
    Wire.beginTransmission(addr);
    Wire.write(0x7F);
    Wire.write(bank_commands[i]);
    if (Wire.endTransmission() != 0) continue;
    
    delay(10);
    
    // 读取WHO_AM_I
    Wire.beginTransmission(addr);
    Wire.write(0x01);
    if (Wire.endTransmission() != 0) continue;
    
    Wire.requestFrom(addr, (uint8_t)1);
    if (Wire.available()) {
      uint8_t who_am_i = Wire.read();
      Serial.printf("\n    Bank=0x%02X, WHO_AM_I=0x%02X", bank_commands[i], who_am_i);
      if (who_am_i == 0x6A) {
        Serial.print(" ✓正确!");
      }
    }
  }
}

// 额外的硬件测试函数
void testHardwareConnections() {
  Serial.println("\n2. 硬件连接测试");
  
  // 测试引脚是否能正常输出
  pinMode(SDA_PIN, OUTPUT);
  pinMode(SCL_PIN, OUTPUT);
  
  Serial.println("测试引脚输出能力...");
  
  for (int i = 0; i < 5; i++) {
    digitalWrite(SDA_PIN, HIGH);
    digitalWrite(SCL_PIN, HIGH);
    delay(100);
    digitalWrite(SDA_PIN, LOW);
    digitalWrite(SCL_PIN, LOW);
    delay(100);
  }
  
  Serial.println("引脚测试完成，重新初始化I2C...");
  
  // 重新初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000);
  delay(100);
}

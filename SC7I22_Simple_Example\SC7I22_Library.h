#ifndef SC7I22_LIBRARY_H
#define SC7I22_LIBRARY_H

#include <Arduino.h>
#include <Wire.h>

// SC7I22配置选项
#define SC7I22_SDO_TO_GND     0
#define SC7I22_SDO_TO_VDD     1

// 采样率选项
enum SC7I22_SampleRate {
  SC7I22_25HZ   = 0x84,
  SC7I22_50HZ   = 0x86,
  SC7I22_100HZ  = 0x88,
  SC7I22_200HZ  = 0x8A,
  SC7I22_400HZ  = 0x8C,
  SC7I22_800HZ  = 0x8E,
  SC7I22_1600HZ = 0x8F
};

// 加速度计量程选项
enum SC7I22_AccRange {
  SC7I22_ACC_2G  = 0x01,
  SC7I22_ACC_4G  = 0x02,
  SC7I22_ACC_8G  = 0x03,
  SC7I22_ACC_16G = 0x04
};

// 陀螺仪量程选项
enum SC7I22_GyrRange {
  SC7I22_GYR_2000DPS = 0x00,
  SC7I22_GYR_1000DPS = 0x01,
  SC7I22_GYR_500DPS  = 0x02,
  SC7I22_GYR_250DPS  = 0x03,
  SC7I22_GYR_125DPS  = 0x04
};

// IMU数据结构
struct SC7I22_Data {
  // 原始数据
  int16_t acc_x, acc_y, acc_z;
  int16_t gyr_x, gyr_y, gyr_z;
  
  // 物理单位数据
  float acc_g_x, acc_g_y, acc_g_z;        // 加速度 (g)
  float gyr_dps_x, gyr_dps_y, gyr_dps_z;  // 角速度 (度/秒)
  
  // 时间戳
  uint32_t timestamp;
};

class SC7I22 {
private:
  uint8_t _i2c_addr;
  uint8_t _sda_pin;
  uint8_t _scl_pin;
  
  SC7I22_AccRange _acc_range;
  SC7I22_GyrRange _gyr_range;
  
  float _acc_scale;
  float _gyr_scale;
  
  // 内部函数
  bool writeReg(uint8_t reg, uint8_t data);
  bool readReg(uint8_t reg, uint8_t* data, uint8_t len);
  void updateScales();
  
public:
  // 构造函数
  SC7I22(uint8_t sdo_pin_state = SC7I22_SDO_TO_VDD, 
         uint8_t sda_pin = 8, uint8_t scl_pin = 9);
  
  // 初始化函数
  bool begin(uint32_t i2c_freq = 400000);
  
  // 配置函数
  bool setAccRange(SC7I22_AccRange range);
  bool setGyrRange(SC7I22_GyrRange range);
  bool setSampleRate(SC7I22_SampleRate acc_rate, SC7I22_SampleRate gyr_rate);
  
  // 数据读取函数
  bool readData(SC7I22_Data& data);
  bool readRawData(int16_t acc[3], int16_t gyr[3]);
  
  // 工具函数
  bool checkConnection();
  uint32_t readTimestamp();
  bool powerDown();
  
  // 获取当前配置
  SC7I22_AccRange getAccRange() { return _acc_range; }
  SC7I22_GyrRange getGyrRange() { return _gyr_range; }
};

#endif

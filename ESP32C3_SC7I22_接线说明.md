# ESP32-C3 与 SC7I22 IMU 接线说明

## 硬件连接

### I2C接线方式（推荐）

| SC7I22引脚 | ESP32-C3引脚 | 说明 |
|-----------|-------------|------|
| VDD       | 3.3V        | 电源正极 |
| GND       | GND         | 电源负极 |
| SDA       | GPIO8       | I2C数据线 |
| SCL       | GPIO9       | I2C时钟线 |
| SDO       | 3.3V 或 GND | 决定I2C地址 |
| CS        | 3.3V        | I2C模式时接高电平 |

### SDO引脚配置说明

- **SDO接GND**: I2C地址为 0x18
- **SDO接VDD**: I2C地址为 0x19

**注意**: 代码中的配置必须与实际接线一致！

## 软件配置

### 1. 基本使用（推荐新手）

使用提供的 `SC7I22_ESP32C3_Arduino.ino` 文件：

```cpp
// 在代码开头确认SDO引脚配置
#define SC7I22_SDO_VDD_GND  1  // 1=SDO接VDD, 0=SDO接GND

// I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9
```

### 2. 库文件使用（推荐进阶用户）

使用 `SC7I22_Library.h` 和 `SC7I22_Library.cpp`：

```cpp
#include "SC7I22_Library.h"

// 创建实例（SDO状态, SDA引脚, SCL引脚）
SC7I22 imu(SC7I22_SDO_TO_VDD, 8, 9);

void setup() {
  if (imu.begin()) {
    // 初始化成功
  }
}

void loop() {
  SC7I22_Data data;
  if (imu.readData(data)) {
    // 使用data.acc_g_x等获取数据
  }
}
```

## 功能特点

### 传感器规格
- **加速度计**: ±2G/±4G/±8G/±16G 可选量程
- **陀螺仪**: ±125/±250/±500/±1000/±2000 dps 可选量程
- **采样率**: 25Hz ~ 1600Hz 可配置
- **数据精度**: 16位

### 工作模式
1. **STREAM模式**（默认）: 实时读取最新数据
2. **FIFO模式**: 批量读取缓存数据

## 使用步骤

### 1. 硬件准备
- 按照接线表连接ESP32-C3和SC7I22
- 确认SDO引脚接线状态
- 检查电源供电（3.3V）

### 2. 软件配置
- 在Arduino IDE中安装ESP32开发板支持
- 将代码文件复制到Arduino项目文件夹
- 根据实际接线修改引脚和地址配置

### 3. 上传测试
- 编译并上传代码到ESP32-C3
- 打开串口监视器（115200波特率）
- 观察IMU数据输出

## 常见问题排查

### 1. 初始化失败
- 检查I2C接线是否正确
- 确认SDO引脚配置与代码一致
- 检查电源供电是否稳定

### 2. 数据读取异常
- 检查I2C时钟频率（建议400kHz）
- 确认传感器工作模式配置
- 检查数据就绪状态

### 3. 数据不稳定
- 增加电源滤波电容
- 检查I2C上拉电阻（通常4.7kΩ）
- 确认接地良好

## 输出数据格式

```
=== SC7I22 IMU数据 ===
加速度计 (g): X=0.012, Y=-0.003, Z=1.001
陀螺仪 (dps): X=0.15, Y=-0.23, Z=0.08
原始数据 - ACC: 393,-98,32801  GYR: 5,-7,3
```

## 进阶功能

### 自定义配置示例
```cpp
// 设置高精度低噪声模式
imu.setAccRange(SC7I22_ACC_2G);      // 最小量程获得最高精度
imu.setGyrRange(SC7I22_GYR_250DPS);  // 较小量程减少噪声
imu.setSampleRate(SC7I22_400HZ, SC7I22_400HZ); // 高采样率

// 设置高动态范围模式
imu.setAccRange(SC7I22_ACC_16G);     // 大量程适应高冲击
imu.setGyrRange(SC7I22_GYR_2000DPS); // 大量程适应快速旋转
```

### 数据处理建议
- 可以添加低通滤波减少噪声
- 可以进行温度补偿提高精度
- 可以实现姿态解算算法（如互补滤波、卡尔曼滤波）
